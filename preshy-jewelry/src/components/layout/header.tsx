"use client"

import React, { useState } from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { categories } from "@/data/products"

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)
  
  return (
    <header className="bg-background border-b border-border">
      {/* Top Bar */}
      <div className="bg-primary text-white py-2 text-center text-sm">
        <p>Free shipping on orders over $100 | Use code WELCOME10 for 10% off your first order</p>
      </div>
      
      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Mobile Menu Button */}
          <button 
            className="lg:hidden p-2 text-foreground"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle menu"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
          
          {/* Logo */}
          <div className="flex-1 lg:flex-none text-center lg:text-left">
            <Link href="/" className="inline-block">
              <h1 className="text-2xl font-display font-bold text-primary">
                Preshy<span className="text-secondary">Jewelry</span>
              </h1>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link href="/" className="text-foreground hover:text-secondary transition-colors">
              Home
            </Link>
            {categories.map((category) => (
              <div key={category.id} className="relative group">
                <Link 
                  href={`/category/${category.slug}`} 
                  className="text-foreground hover:text-secondary transition-colors"
                >
                  {category.name}
                </Link>
                {category.subcategories && category.subcategories.length > 0 && (
                  <div className="absolute left-0 mt-2 w-48 bg-background border border-border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="p-4 space-y-2">
                      {category.subcategories.map((subcategory) => (
                        <Link 
                          key={subcategory.id}
                          href={`/category/${category.slug}/${subcategory.slug}`}
                          className="block text-foreground hover:text-secondary transition-colors"
                        >
                          {subcategory.name}
                        </Link>
                      ))}
                      <Link 
                        href={`/category/${category.slug}`}
                        className="block text-secondary font-medium hover:underline mt-2"
                      >
                        View All {category.name}
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            ))}
            <Link href="/custom" className="text-foreground hover:text-secondary transition-colors">
              Custom Jewelry
            </Link>
            <Link href="/about" className="text-foreground hover:text-secondary transition-colors">
              About Us
            </Link>
            <Link href="/contact" className="text-foreground hover:text-secondary transition-colors">
              Contact
            </Link>
          </nav>
          
          {/* Icons */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => setSearchOpen(!searchOpen)}
              className="p-2 text-foreground hover:text-secondary transition-colors"
              aria-label="Search"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
            </button>
            <Link 
              href="/account" 
              className="p-2 text-foreground hover:text-secondary transition-colors"
              aria-label="Account"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
              </svg>
            </Link>
            <Link 
              href="/wishlist" 
              className="p-2 text-foreground hover:text-secondary transition-colors"
              aria-label="Wishlist"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z" />
              </svg>
            </Link>
            <Link 
              href="/cart" 
              className="p-2 text-foreground hover:text-secondary transition-colors relative"
              aria-label="Cart"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
              </svg>
              <span className="absolute -top-1 -right-1 bg-secondary text-primary text-xs w-4 h-4 flex items-center justify-center rounded-full">
                0
              </span>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Mobile Menu */}
      <div className={cn(
        "fixed inset-0 bg-background z-50 lg:hidden transition-transform duration-300 ease-in-out",
        mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="p-4 flex justify-between items-center border-b border-border">
          <h2 className="text-xl font-display font-bold">Menu</h2>
          <button 
            onClick={() => setMobileMenuOpen(false)}
            className="p-2 text-foreground"
            aria-label="Close menu"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <nav className="p-4 space-y-4">
          <Link 
            href="/" 
            className="block py-2 text-foreground hover:text-secondary transition-colors"
            onClick={() => setMobileMenuOpen(false)}
          >
            Home
          </Link>
          {categories.map((category) => (
            <div key={category.id} className="py-2">
              <Link 
                href={`/category/${category.slug}`} 
                className="block text-foreground hover:text-secondary transition-colors font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                {category.name}
              </Link>
              {category.subcategories && category.subcategories.length > 0 && (
                <div className="ml-4 mt-2 space-y-2">
                  {category.subcategories.map((subcategory) => (
                    <Link 
                      key={subcategory.id}
                      href={`/category/${category.slug}/${subcategory.slug}`}
                      className="block text-foreground-muted hover:text-secondary transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {subcategory.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
          <Link 
            href="/custom" 
            className="block py-2 text-foreground hover:text-secondary transition-colors"
            onClick={() => setMobileMenuOpen(false)}
          >
            Custom Jewelry
          </Link>
          <Link 
            href="/about" 
            className="block py-2 text-foreground hover:text-secondary transition-colors"
            onClick={() => setMobileMenuOpen(false)}
          >
            About Us
          </Link>
          <Link 
            href="/contact" 
            className="block py-2 text-foreground hover:text-secondary transition-colors"
            onClick={() => setMobileMenuOpen(false)}
          >
            Contact
          </Link>
        </nav>
      </div>
      
      {/* Search Overlay */}
      <div className={cn(
        "fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300",
        searchOpen ? "opacity-100 visible" : "opacity-0 invisible"
      )}>
        <div className="container mx-auto px-4 pt-20">
          <div className="bg-background rounded-lg shadow-xl p-6 max-w-2xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-display font-bold">Search Products</h2>
              <button 
                onClick={() => setSearchOpen(false)}
                className="p-2 text-foreground hover:text-secondary transition-colors"
                aria-label="Close search"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="relative">
              <input 
                type="text" 
                placeholder="Search for jewelry..." 
                className="w-full border border-border rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-secondary"
              />
              <button className="absolute right-3 top-1/2 -translate-y-1/2 text-foreground-muted hover:text-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
              </button>
            </div>
            <div className="mt-4">
              <h3 className="text-sm font-medium text-foreground-muted mb-2">Popular Searches</h3>
              <div className="flex flex-wrap gap-2">
                <Link href="/search?q=diamond+rings" className="text-sm bg-background-secondary px-3 py-1 rounded-full hover:bg-border-light transition-colors">
                  Diamond Rings
                </Link>
                <Link href="/search?q=gold+necklaces" className="text-sm bg-background-secondary px-3 py-1 rounded-full hover:bg-border-light transition-colors">
                  Gold Necklaces
                </Link>
                <Link href="/search?q=pearl+earrings" className="text-sm bg-background-secondary px-3 py-1 rounded-full hover:bg-border-light transition-colors">
                  Pearl Earrings
                </Link>
                <Link href="/search?q=engagement" className="text-sm bg-background-secondary px-3 py-1 rounded-full hover:bg-border-light transition-colors">
                  Engagement
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
