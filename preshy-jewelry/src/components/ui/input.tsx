import * as React from "react"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  variant?: "default" | "filled" | "outlined"
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type = "text", 
    label, 
    error, 
    leftIcon, 
    rightIcon, 
    variant = "default",
    id,
    ...props 
  }, ref) => {
    const inputId = id || React.useId()
    
    const baseStyles = "flex w-full rounded-lg border transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-secondary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
    
    const variants = {
      default: "border-border bg-background hover:border-primary focus:border-primary",
      filled: "border-transparent bg-background-secondary hover:bg-border-light focus:bg-background focus:border-primary",
      outlined: "border-2 border-border bg-transparent hover:border-primary focus:border-primary"
    }
    
    const sizeStyles = leftIcon || rightIcon ? "px-10 py-2.5" : "px-3 py-2.5"
    const errorStyles = error ? "border-error focus:border-error focus:ring-error" : ""

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className="text-sm font-medium text-foreground"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-foreground-muted">
              {leftIcon}
            </div>
          )}
          <input
            type={type}
            id={inputId}
            className={cn(
              baseStyles,
              variants[variant],
              sizeStyles,
              errorStyles,
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              className
            )}
            ref={ref}
            {...props}
          />
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-foreground-muted">
              {rightIcon}
            </div>
          )}
        </div>
        {error && (
          <p className="text-sm text-error">{error}</p>
        )}
      </div>
    )
  }
)

Input.displayName = "Input"

export { Input }
