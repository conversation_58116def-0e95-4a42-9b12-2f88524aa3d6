import * as React from "react"
import { cn } from "@/lib/utils"

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "secondary" | "success" | "warning" | "error" | "outline"
  size?: "sm" | "md" | "lg"
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant = "default", size = "md", ...props }, ref) => {
    const baseStyles = "inline-flex items-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2"
    
    const variants = {
      default: "bg-primary text-white",
      secondary: "bg-secondary text-primary",
      success: "bg-success text-white",
      warning: "bg-warning text-white",
      error: "bg-error text-white",
      outline: "border border-border text-foreground bg-transparent"
    }
    
    const sizes = {
      sm: "px-2 py-0.5 text-xs rounded-md",
      md: "px-2.5 py-1 text-sm rounded-lg",
      lg: "px-3 py-1.5 text-base rounded-lg"
    }

    return (
      <div
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      />
    )
  }
)

Badge.displayName = "Badge"

export { Badge }
