// Product Types
export interface Product {
  id: string
  name: string
  description: string
  shortDescription?: string
  price: number
  salePrice?: number
  sku: string
  category: ProductCategory
  subcategory?: string
  images: ProductImage[]
  materials: Material[]
  gemstones?: Gemstone[]
  dimensions: Dimensions
  weight: number
  inStock: boolean
  stockQuantity: number
  isCustomizable: boolean
  customizationOptions?: CustomizationOption[]
  tags: string[]
  rating: number
  reviewCount: number
  createdAt: string
  updatedAt: string
  featured?: boolean
  isNew?: boolean
  isBestseller?: boolean
}

export interface ProductImage {
  id: string
  url: string
  alt: string
  isPrimary: boolean
  order: number
}

export interface Material {
  id: string
  name: string
  type: "metal" | "gemstone" | "other"
  purity?: string
  color?: string
  finish?: string
}

export interface Gemstone {
  id: string
  name: string
  type: string
  cut: string
  carat: number
  color: string
  clarity: string
  certification?: string
}

export interface Dimensions {
  length: number
  width: number
  height?: number
  diameter?: number
  unit: "mm" | "cm" | "inch"
}

export interface CustomizationOption {
  id: string
  name: string
  type: "metal" | "gemstone" | "size" | "engraving"
  options: CustomizationChoice[]
  required: boolean
  priceModifier: number
}

export interface CustomizationChoice {
  id: string
  name: string
  value: string
  priceModifier: number
  image?: string
}

// Category Types
export type ProductCategory = 
  | "rings" 
  | "necklaces" 
  | "bracelets" 
  | "earrings" 
  | "watches" 
  | "custom"

export interface Category {
  id: string
  name: string
  slug: string
  description: string
  image: string
  subcategories?: Subcategory[]
  productCount: number
}

export interface Subcategory {
  id: string
  name: string
  slug: string
  description: string
  image?: string
  productCount: number
}

// Cart Types
export interface CartItem {
  id: string
  productId: string
  product: Product
  quantity: number
  customizations?: Record<string, string>
  price: number
  totalPrice: number
}

export interface Cart {
  items: CartItem[]
  subtotal: number
  tax: number
  shipping: number
  total: number
  itemCount: number
}

// User Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  avatar?: string
  addresses: Address[]
  preferences: UserPreferences
  createdAt: string
  updatedAt: string
}

export interface Address {
  id: string
  type: "billing" | "shipping"
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postalCode: string
  country: string
  phone?: string
  isDefault: boolean
}

export interface UserPreferences {
  currency: string
  language: string
  notifications: {
    email: boolean
    sms: boolean
    push: boolean
  }
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  userId: string
  status: OrderStatus
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  total: number
  shippingAddress: Address
  billingAddress: Address
  paymentMethod: PaymentMethod
  trackingNumber?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  productId: string
  product: Product
  quantity: number
  customizations?: Record<string, string>
  price: number
  totalPrice: number
}

export type OrderStatus = 
  | "pending" 
  | "confirmed" 
  | "processing" 
  | "shipped" 
  | "delivered" 
  | "cancelled" 
  | "refunded"

export interface PaymentMethod {
  id: string
  type: "card" | "paystack" | "bank_transfer"
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
}

// Review Types
export interface Review {
  id: string
  productId: string
  userId: string
  user: {
    firstName: string
    lastName: string
    avatar?: string
  }
  rating: number
  title: string
  comment: string
  images?: string[]
  verified: boolean
  helpful: number
  createdAt: string
  updatedAt: string
}

// Filter Types
export interface ProductFilters {
  category?: ProductCategory
  subcategory?: string
  priceRange?: [number, number]
  materials?: string[]
  gemstones?: string[]
  inStock?: boolean
  rating?: number
  sortBy?: "price_asc" | "price_desc" | "name_asc" | "name_desc" | "rating" | "newest"
  search?: string
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message: string
  success: boolean
}
