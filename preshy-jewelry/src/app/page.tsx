import { Head<PERSON> } from "@/components/layout/header"
import { <PERSON><PERSON> } from "@/components/layout/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { categories, sampleProducts } from "@/data/products"
import { formatPrice } from "@/lib/utils"
import Link from "next/link"
import Image from "next/image"

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-primary to-primary-light text-white py-20 lg:py-32">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <h1 className="text-4xl lg:text-6xl font-display font-bold leading-tight">
                  Where Luxury Meets
                  <span className="text-secondary block">Affordability</span>
                </h1>
                <p className="text-xl text-gray-200 leading-relaxed">
                  Discover exquisite handcrafted jewelry that celebrates life's precious moments.
                  From engagement rings to custom designs, find the perfect piece for every occasion.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" variant="secondary" className="text-lg">
                    Shop Collection
                  </Button>
                  <Button size="lg" variant="outline" className="text-lg border-white text-white hover:bg-white hover:text-primary">
                    Custom Design
                  </Button>
                </div>
                <div className="flex items-center space-x-8 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">10K+</div>
                    <div className="text-sm text-gray-300">Happy Customers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">5★</div>
                    <div className="text-sm text-gray-300">Average Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">500+</div>
                    <div className="text-sm text-gray-300">Unique Designs</div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                  <div className="aspect-square bg-gradient-to-br from-secondary to-secondary-light rounded-xl flex items-center justify-center">
                    <div className="text-6xl">💎</div>
                  </div>
                  <div className="mt-6 text-center">
                    <h3 className="text-xl font-semibold mb-2">Featured Collection</h3>
                    <p className="text-gray-300">Handpicked luxury pieces</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Categories Section */}
        <section className="py-16 bg-background-secondary">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-display font-bold mb-4">Shop by Category</h2>
              <p className="text-foreground-muted text-lg max-w-2xl mx-auto">
                Explore our curated collections of fine jewelry, each piece crafted with precision and passion.
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {categories.map((category) => (
                <Link key={category.id} href={`/category/${category.slug}`}>
                  <Card hover className="text-center group">
                    <CardContent padding="lg">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-secondary to-secondary-light rounded-full flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-200">
                        {category.id === 'rings' && '💍'}
                        {category.id === 'necklaces' && '📿'}
                        {category.id === 'bracelets' && '⚡'}
                        {category.id === 'earrings' && '👂'}
                        {category.id === 'watches' && '⌚'}
                      </div>
                      <h3 className="font-semibold text-lg mb-2">{category.name}</h3>
                      <p className="text-foreground-muted text-sm mb-3">{category.description}</p>
                      <Badge variant="outline" size="sm">{category.productCount} items</Badge>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Products */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-display font-bold mb-4">Featured Products</h2>
              <p className="text-foreground-muted text-lg max-w-2xl mx-auto">
                Discover our most popular and newest jewelry pieces, handpicked for their exceptional quality and design.
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {sampleProducts.map((product) => (
                <Link key={product.id} href={`/product/${product.id}`}>
                  <Card hover className="group">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <div className="text-4xl">💎</div>
                      </div>
                      {product.salePrice && (
                        <Badge className="absolute top-3 left-3" variant="error">
                          Save {Math.round(((product.price - product.salePrice) / product.price) * 100)}%
                        </Badge>
                      )}
                      {product.isNew && (
                        <Badge className="absolute top-3 right-3" variant="success">
                          New
                        </Badge>
                      )}
                    </div>
                    <CardContent padding="lg">
                      <div className="space-y-3">
                        <h3 className="font-semibold text-lg group-hover:text-secondary transition-colors">
                          {product.name}
                        </h3>
                        <p className="text-foreground-muted text-sm line-clamp-2">
                          {product.shortDescription}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              {product.salePrice ? (
                                <>
                                  <span className="text-lg font-bold text-secondary">
                                    {formatPrice(product.salePrice)}
                                  </span>
                                  <span className="text-sm text-foreground-muted line-through">
                                    {formatPrice(product.price)}
                                  </span>
                                </>
                              ) : (
                                <span className="text-lg font-bold">
                                  {formatPrice(product.price)}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center space-x-1">
                              <div className="flex text-secondary">
                                {[...Array(5)].map((_, i) => (
                                  <span key={i} className={i < Math.floor(product.rating) ? "text-secondary" : "text-gray-300"}>
                                    ★
                                  </span>
                                ))}
                              </div>
                              <span className="text-sm text-foreground-muted">
                                ({product.reviewCount})
                              </span>
                            </div>
                          </div>
                          <Button size="sm" variant="outline" className="opacity-0 group-hover:opacity-100 transition-opacity">
                            View Details
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
            <div className="text-center mt-12">
              <Button size="lg" variant="outline">
                View All Products
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-background-secondary">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-secondary rounded-full flex items-center justify-center text-white text-2xl">
                  🚚
                </div>
                <h3 className="font-semibold text-lg mb-2">Free Shipping</h3>
                <p className="text-foreground-muted">Free shipping on orders over $100</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-secondary rounded-full flex items-center justify-center text-white text-2xl">
                  🔒
                </div>
                <h3 className="font-semibold text-lg mb-2">Secure Payment</h3>
                <p className="text-foreground-muted">Your payment information is safe with us</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-secondary rounded-full flex items-center justify-center text-white text-2xl">
                  ↩️
                </div>
                <h3 className="font-semibold text-lg mb-2">Easy Returns</h3>
                <p className="text-foreground-muted">30-day return policy for your peace of mind</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-secondary rounded-full flex items-center justify-center text-white text-2xl">
                  💎
                </div>
                <h3 className="font-semibold text-lg mb-2">Quality Guarantee</h3>
                <p className="text-foreground-muted">Lifetime warranty on all our jewelry</p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
