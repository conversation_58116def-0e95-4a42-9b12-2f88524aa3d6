import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Preshy Jewelry - Luxury Jewelry & Custom Designs",
  description: "Discover exquisite handcrafted jewelry, engagement rings, necklaces, bracelets, and custom designs. Where luxury meets affordability.",
  keywords: "jewelry, luxury jewelry, engagement rings, necklaces, bracelets, earrings, custom jewelry, diamonds, gold, silver",
  authors: [{ name: "Preshy Jewelry" }],
  creator: "Preshy Jewelry",
  publisher: "Preshy Jewelry",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://preshyjewelry.com",
    title: "Preshy Jewelry - Luxury Jewelry & Custom Designs",
    description: "Discover exquisite handcrafted jewelry, engagement rings, necklaces, bracelets, and custom designs. Where luxury meets affordability.",
    siteName: "Preshy Jewelry",
  },
  twitter: {
    card: "summary_large_image",
    title: "Preshy Jewelry - Luxury Jewelry & Custom Designs",
    description: "Discover exquisite handcrafted jewelry, engagement rings, necklaces, bracelets, and custom designs. Where luxury meets affordability.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${playfairDisplay.variable}`}>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
