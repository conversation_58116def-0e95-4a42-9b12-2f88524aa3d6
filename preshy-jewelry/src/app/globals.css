@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Brand Colors */
  --primary: #1a1a1a;
  --primary-light: #2d2d2d;
  --secondary: #d4af37;
  --secondary-light: #e6c866;
  --accent: #8b4513;
  --accent-light: #a0522d;

  /* Neutral Colors */
  --background: #ffffff;
  --background-secondary: #fafafa;
  --foreground: #1a1a1a;
  --foreground-muted: #6b7280;
  --border: #e5e7eb;
  --border-light: #f3f4f6;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@theme inline {
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-secondary-light: var(--secondary-light);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-background: var(--background);
  --color-background-secondary: var(--background-secondary);
  --color-foreground: var(--foreground);
  --color-foreground-muted: var(--foreground-muted);
  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  --font-display: 'Playfair Display', serif;
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: ui-monospace, 'SF Mono', monospace;

  --shadow-sm: var(--shadow-sm);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --background-secondary: #1a1a1a;
    --foreground: #ffffff;
    --foreground-muted: #9ca3af;
    --border: #374151;
    --border-light: #1f2937;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--foreground-muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2;
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
